'use client'

import { TonConnectUIProvider } from '@tonconnect/ui-react'
import { ReactNode } from 'react'

interface TonConnectProviderProps {
  children: ReactNode
}

const manifestUrl = '/tonconnect-manifest.json'

export default function TonConnectProvider({ children }: TonConnectProviderProps) {
  return (
    <TonConnectUIProvider 
      manifestUrl={manifestUrl}
      actionsConfiguration={{
        twaReturnUrl: 'https://t.me/your_bot_name' // Replace with your actual bot URL
      }}
    >
      {children}
    </TonConnectUIProvider>
  )
}
